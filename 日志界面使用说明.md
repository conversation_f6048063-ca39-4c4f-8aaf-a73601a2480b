# 设备日志界面使用说明

## 功能概述

新创建的设备日志界面允许用户查看所有在线设备（无人机和机场）的日志文件列表。

## 主要功能

### 1. 设备日志列表显示
- 显示设备的基本信息（序列号、类型、在线状态等）
- 列出该设备的所有日志文件
- 显示每个日志文件的详细信息：
  - 启动索引
  - 开始时间和结束时间
  - 持续时间
  - 文件大小

### 2. 访问方式
- 在设备列表页面（机场设备或无人机设备）
- 点击在线设备操作栏中的"日志"按钮
- 系统会自动跳转到日志界面

### 3. API接口
调用接口：`/device/logs/logs?deviceSn={设备序列号}&domain_list={域类型}`
- `deviceSn`: 设备的序列号
- `domain_list`: 域类型（3=机场，0=无人机）

### 4. 界面特性
- 响应式设计，适配不同屏幕尺寸
- 实时刷新功能
- 空状态提示
- 错误处理和用户友好的提示信息

## 文件结构

### 新增文件
1. `src/views/device/deviceLogs.vue` - 日志界面组件
2. `src/api/device.js` - 添加了日志API接口

### 修改文件
1. `src/views/device/dockIndex.vue` - 机场设备列表，添加日志按钮
2. `src/views/device/droneIndex.vue` - 无人机设备列表，添加日志按钮
3. `src/router/index.js` - 添加日志页面路由配置

## 技术实现

### 路由配置
```javascript
{
  path: '/device',
  component: Layout,
  hidden: true,
  permissions: ['device:query'],
  children: [
    {
      path: 'logs',
      component: () => import('@/views/device/deviceLogs'),
      name: 'DeviceLogs',
      meta: { title: '设备日志', activeMenu: '/device/dockIndex' }
    }
  ]
}
```

### API接口
```javascript
// 获取设备日志列表
export function getDeviceLogs(deviceSn, domainList) {
  return request({
    url: '/device/logs/logs',
    method: 'get',
    params: {
      deviceSn: deviceSn,
      domain_list: domainList
    }
  })
}
```

## 使用步骤

1. 进入设备管理页面（机场设备或无人机设备）
2. 找到在线状态的设备
3. 点击操作栏中的"日志"按钮
4. 系统跳转到日志界面，显示该设备的日志文件列表
5. 可以查看日志文件的详细信息
6. 点击"返回"按钮回到设备列表

## 注意事项

1. 只有在线设备才显示日志按钮
2. 日志查看和下载功能目前为占位符，需要后续实现
3. 界面支持中文显示，时间格式为本地化格式
4. 文件大小自动转换为合适的单位（B、KB、MB、GB等）

## 后续扩展

1. 实现日志文件内容查看功能
2. 实现日志文件下载功能
3. 添加日志文件搜索和过滤功能
4. 支持日志文件分页显示
5. 添加日志文件删除功能（如果需要）
