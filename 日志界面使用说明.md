# 设备日志管理系统使用说明

## 功能概述

新创建的设备日志管理系统包含两个主要界面：
1. **设备日志列表页面** - 显示所有支持日志查看的设备
2. **设备日志详情页面** - 显示特定设备的日志文件列表

## 主要功能

### 1. 设备日志列表页面 (`/device/logs`)
- 显示所有机场和无人机设备
- 支持按设备序列号、昵称、类型、在线状态筛选
- 只有在线设备才能查看日志
- 点击"查看日志"按钮跳转到日志详情页面

### 2. 设备日志详情页面 (`/device/logs/detail`)
- 显示设备的基本信息（序列号、类型、在线状态等）
- 列出该设备的所有日志文件
- 显示每个日志文件的详细信息：
  - 启动索引
  - 开始时间和结束时间
  - 持续时间
  - 文件大小

### 3. 访问方式
- 直接访问设备日志列表页面：`/device/logs`
- 在日志列表页面点击"查看日志"按钮
- 系统会自动跳转到对应设备的日志详情界面

### 4. API接口
调用接口：`/device/logs/logs?deviceSn={设备序列号}&domain_list={域类型}`
- `deviceSn`: 设备的序列号
- `domain_list`: 域类型（3=机场，0=无人机）

### 5. 界面特性
- 响应式设计，适配不同屏幕尺寸
- 实时刷新功能
- 空状态提示
- 错误处理和用户友好的提示信息
- 支持搜索和筛选功能

## 文件结构

### 新增文件
1. `src/views/device/logs/index.vue` - 设备日志列表页面
2. `src/views/device/logs/detail.vue` - 设备日志详情页面
3. `src/api/device.js` - 添加了日志API接口

### 修改文件
1. `src/router/index.js` - 添加日志页面路由配置
2. `src/views/device/dockIndex.vue` - 移除了原有的日志按钮
3. `src/views/device/droneIndex.vue` - 移除了原有的日志按钮

## 技术实现

### 路由配置
```javascript
{
  path: '/device',
  component: Layout,
  hidden: true,
  permissions: ['device:query'],
  children: [
    {
      path: 'logs',
      component: () => import('@/views/device/logs/index'),
      name: 'DeviceLogsList',
      meta: { title: '设备日志管理' }
    },
    {
      path: 'logs/detail',
      component: () => import('@/views/device/logs/detail'),
      name: 'DeviceLogsDetail',
      meta: { title: '设备日志详情', activeMenu: '/device/logs' }
    }
  ]
}
```

### API接口
```javascript
// 获取设备日志列表
export function getDeviceLogs(deviceSn, domainList) {
  return request({
    url: '/device/logs/logs',
    method: 'get',
    params: {
      deviceSn: deviceSn,
      domain_list: domainList
    }
  })
}
```

## 使用步骤

### 方式一：直接访问日志管理页面
1. 直接访问 `/device/logs` 路径
2. 查看所有支持日志功能的设备列表
3. 使用搜索和筛选功能找到目标设备
4. 点击在线设备的"查看日志"按钮
5. 系统跳转到日志详情页面，显示该设备的日志文件列表
6. 可以查看、下载日志文件
7. 点击"返回"按钮回到日志设备列表

### 方式二：通过菜单导航（需要后台配置菜单）
1. 在系统菜单中添加"设备日志管理"菜单项
2. 菜单路径设置为 `/device/logs`
3. 用户通过菜单导航进入日志管理页面

## 注意事项

1. 只有在线设备才显示日志按钮
2. 日志查看和下载功能目前为占位符，需要后续实现
3. 界面支持中文显示，时间格式为本地化格式
4. 文件大小自动转换为合适的单位（B、KB、MB、GB等）

## 后续扩展

1. 实现日志文件内容查看功能
2. 实现日志文件下载功能
3. 添加日志文件搜索和过滤功能
4. 支持日志文件分页显示
5. 添加日志文件删除功能（如果需要）
