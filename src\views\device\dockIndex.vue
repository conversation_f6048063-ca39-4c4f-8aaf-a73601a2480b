<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="设备序列号" prop="device_sn">
        <el-input
          v-model="queryParams.device_sn"
          placeholder="请输入设备序列号"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
     
      <el-form-item label="设备昵称" prop="nickname">
        <el-input
          v-model="queryParams.nickname"
          placeholder="请输入设备昵称"
          clearable
          style="width: 200px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      
      
      <el-form-item label="绑定状态" prop="bound_status">
        <el-select v-model="queryParams.bound_status" placeholder="请选择绑定状态" clearable style="width: 200px">
          <el-option label="未绑定" :value="false" />
          <el-option label="已绑定" :value="true" />
        </el-select>
      </el-form-item>
      
     
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['device:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['device:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['device:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['device:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList" :columns="columns"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="deviceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="50" align="center" />
      <el-table-column label="设备序列号" align="center" key="device_sn" prop="device_sn" v-if="columns[0].visible" :show-overflow-tooltip="true" />
      <el-table-column label="设备名称" align="center" key="device_name" prop="device_name" v-if="columns[1].visible" :show-overflow-tooltip="true" />
      <el-table-column label="设备昵称" align="center" key="nickname" prop="nickname" v-if="columns[2].visible" :show-overflow-tooltip="true" />

      <el-table-column label="设备类型" align="center" key="type" v-if="columns[6].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.type === 1" type="primary">网关</el-tag>
          <el-tag v-else-if="scope.row.type === 2" type="success">机场</el-tag>
          <el-tag v-else-if="scope.row.type === 3" type="warning">无人机</el-tag>
          <el-tag v-else-if="scope.row.type === 4" type="info">遥控器</el-tag>
          <el-tag v-else type="default">未知</el-tag>
        </template>
      </el-table-column>
 
      <el-table-column label="固件版本" align="center" key="firmware_version" prop="firmware_version" v-if="columns[10].visible" :show-overflow-tooltip="true" />
      <el-table-column label="固件状态" align="center" key="firmware_status" v-if="columns[11].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.firmware_status === 1" type="success">正常</el-tag>
          <el-tag v-else-if="scope.row.firmware_status === 2" type="danger">升级</el-tag>
          <el-tag v-else-if="scope.row.firmware_status === 3" type="danger">一致升级</el-tag>
          <el-tag v-else-if="scope.row.firmware_status === 4" type="danger">升级期间</el-tag>
          <el-tag v-else type="warning">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="协议版本" align="center" key="thing_version" prop="thing_version" v-if="columns[12].visible" :show-overflow-tooltip="true" />
      <el-table-column label="绑定状态" align="center" key="bound_status" v-if="columns[13].visible">
        <template slot-scope="scope">
          <el-switch
            v-model="scope.row.bound_status"
            :active-value="true"
            :inactive-value="false"
            @change="handleBoundStatusChange(scope.row)"
          ></el-switch>
        </template>
      </el-table-column>

      <el-table-column label="在线状态" align="center" key="mode_code" v-if="columns[16].visible">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.mode_code >= 0" type="success">在线</el-tag>
          <el-tag v-else type="danger">离线</el-tag>
        </template>
      </el-table-column>

      <el-table-column label="绑定时间" align="center" prop="bound_time" v-if="columns[14].visible" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.bound_time || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="最后登录" align="center" prop="login_time" v-if="columns[15].visible" width="180">
        <template slot-scope="scope">
          <span>{{ scope.row.login_time || '-' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" width="200" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['device:query']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['device:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['device:remove']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 添加或修改设备配置对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="100px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备序列号" prop="device_sn">
              <el-input v-model="form.device_sn" placeholder="请输入设备序列号" maxlength="32" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="设备名称" prop="device_name">
              <el-input v-model="form.device_name" placeholder="请输入设备名称" maxlength="64" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="设备昵称" prop="nickname">
              <el-input v-model="form.nickname" placeholder="请输入设备昵称" maxlength="64" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工作空间ID" prop="workspace_id">
              <el-input v-model="form.workspace_id" placeholder="请输入工作空间ID" maxlength="64" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          
          <el-col :span="12">
            <el-form-item label="设备类型" prop="type">
              <el-select v-model="form.type" placeholder="请选择设备类型">
                <el-option label="机场" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
       
        <el-row>
          <el-col :span="12">
            <el-form-item label="固件版本" prop="firmware_version">
              <el-input v-model="form.firmware_version" placeholder="请输入固件版本" maxlength="32" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="固件状态" prop="firmware_status">
              <el-radio-group v-model="form.firmware_status">
                <el-radio :label="1">正常</el-radio>
                <el-radio :label="0">异常</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="协议版本" prop="thing_version">
              <el-input v-model="form.thing_version" placeholder="请输入协议版本" maxlength="32" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="绑定状态" prop="bound_status">
              <el-radio-group v-model="form.bound_status">
                <el-radio :label="true">已绑定</el-radio>
                <el-radio :label="false">未绑定</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="普通图标URL" prop="normal_icon_url">
              <el-input v-model="form.normal_icon_url" placeholder="请输入普通图标URL" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="选中图标URL" prop="selected_icon_url">
              <el-input v-model="form.selected_icon_url" placeholder="请输入选中图标URL" maxlength="200" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="24">
            <el-form-item label="设备描述" prop="device_desc">
              <el-input v-model="form.device_desc" type="textarea" placeholder="请输入设备描述" maxlength="100"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 机场设备详情对话框 -->
    <el-dialog title="机场设备详情" :visible.sync="detailOpen" width="1200px" append-to-body>
      <div class="dock-detail-container">
        <el-tabs v-model="activeTab" type="border-card">
          <!-- 基本信息 -->
          <el-tab-pane label="基本信息" name="basic">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-descriptions title="设备信息" :column="1" border>
                  <el-descriptions-item label="设备序列号">{{ currentDevice.device_sn }}</el-descriptions-item>
                  <el-descriptions-item label="设备名称">{{ currentDevice.device_name }}</el-descriptions-item>
                  <el-descriptions-item label="设备昵称">{{ currentDevice.nickname }}</el-descriptions-item>
                  <el-descriptions-item label="固件版本">{{ currentDevice.firmware_version }}</el-descriptions-item>
                  <el-descriptions-item label="协议版本">{{ currentDevice.thing_version }}</el-descriptions-item>
                  <el-descriptions-item label="绑定状态">
                    <el-tag :type="currentDevice.bound_status ? 'success' : 'danger'">
                      {{ currentDevice.bound_status ? '已绑定' : '未绑定' }}
                    </el-tag>
                  </el-descriptions-item>
                </el-descriptions>
              </el-col>
              <el-col :span="12">
                <el-descriptions title="位置信息" :column="1" border>
                  <el-descriptions-item label="经度">{{ dockInfo.longitude }}°</el-descriptions-item>
                  <el-descriptions-item label="纬度">{{ dockInfo.latitude }}°</el-descriptions-item>
                  <el-descriptions-item label="高度">{{ dockInfo.height }}m</el-descriptions-item>
                  <el-descriptions-item label="GPS状态">
                    <el-tag :type="dockInfo.position_state && dockInfo.position_state.is_fixed === 2 ? 'success' : 'warning'">
                      {{ dockInfo.position_state && dockInfo.position_state.is_fixed === 2 ? 'RTK固定解' : 'GPS定位' }}
                    </el-tag>
                  </el-descriptions-item>
                  <el-descriptions-item label="GPS卫星数">{{ dockInfo.position_state && dockInfo.position_state.gps_number || '-' }}</el-descriptions-item>
                  <el-descriptions-item label="RTK卫星数">{{ dockInfo.position_state && dockInfo.position_state.rtk_number || '-' }}</el-descriptions-item>
                </el-descriptions>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 环境信息 -->
          <el-tab-pane label="环境信息" name="environment">
            <el-row :gutter="20">
              <el-col :span="8">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>气象信息</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="环境温度">{{ dockInfo.environment_temperature }}°C</el-descriptions-item>
                    <el-descriptions-item label="设备温度">{{ dockInfo.temperature }}°C</el-descriptions-item>
                    <el-descriptions-item label="湿度">{{ dockInfo.humidity }}%</el-descriptions-item>
                    <el-descriptions-item label="风速">{{ dockInfo.wind_speed }}m/s</el-descriptions-item>
                    <el-descriptions-item label="降雨量">{{ dockInfo.rainfall }}mm</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>网络状态</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="网络类型">
                      <el-tag :type="dockInfo.network_state && dockInfo.network_state.type === 1 ? 'success' : 'warning'">
                        {{ getNetworkTypeName(dockInfo.network_state && dockInfo.network_state.type) }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="信号质量">{{ dockInfo.network_state && dockInfo.network_state.quality || '-' }}/5</el-descriptions-item>
                    <el-descriptions-item label="传输速率">{{ dockInfo.network_state && dockInfo.network_state.rate || '-' }}Mbps</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="8">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>存储信息</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="总容量">{{ formatStorage(dockInfo.storage && dockInfo.storage.total) }}</el-descriptions-item>
                    <el-descriptions-item label="已使用">{{ formatStorage(dockInfo.storage && dockInfo.storage.used) }}</el-descriptions-item>
                    <el-descriptions-item label="使用率">{{ getStorageUsagePercent() }}%</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 无人机信息 -->
          <el-tab-pane label="无人机信息" name="drone">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>无人机状态</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="无人机在舱">
                      <el-tag :type="dockInfo.drone_in_dock ? 'success' : 'danger'">
                        {{ dockInfo.drone_in_dock ? '在舱' : '不在舱' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="设备序列号">{{ dockInfo.sub_device && dockInfo.sub_device.device_sn || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="设备型号">{{ dockInfo.sub_device && dockInfo.sub_device.device_model_key || '-' }}</el-descriptions-item>
                    <el-descriptions-item label="配对状态">
                      <el-tag :type="dockInfo.sub_device && dockInfo.sub_device.device_paired ? 'success' : 'danger'">
                        {{ dockInfo.sub_device && dockInfo.sub_device.device_paired ? '已配对' : '未配对' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="在线状态">
                      <el-tag :type="dockInfo.sub_device && dockInfo.sub_device.device_online_status ? 'success' : 'danger'">
                        {{ dockInfo.sub_device && dockInfo.sub_device.device_online_status ? '在线' : '离线' }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>电池信息</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="充电状态">
                      <el-tag :type="dockInfo.drone_charge_state && dockInfo.drone_charge_state.state ? 'primary' : 'info'">
                        {{ dockInfo.drone_charge_state && dockInfo.drone_charge_state.state ? '充电中' : '未充电' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="电池电量">{{ dockInfo.drone_charge_state && dockInfo.drone_charge_state.capacity_percent || '-' }}%</el-descriptions-item>
                    <el-descriptions-item label="维护状态">
                      <el-tag :type="getDroneBatteryMaintenanceStatusType()">
                        {{ getDroneBatteryMaintenanceStatusText() }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                  <div v-if="dockInfo.drone_battery_maintenance_info && dockInfo.drone_battery_maintenance_info.batteries" style="margin-top: 10px;">
                    <h4>电池详情</h4>
                    <el-table :data="dockInfo.drone_battery_maintenance_info.batteries" size="mini" border>
                      <el-table-column prop="index" label="电池编号" width="80"></el-table-column>
                      <el-table-column prop="capacity_percent" label="电量(%)" width="80"></el-table-column>
                      <el-table-column prop="voltage" label="电压(mV)" width="100"></el-table-column>
                      <el-table-column prop="temperature" label="温度(°C)" width="100"></el-table-column>
                    </el-table>
                  </div>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 设备状态 -->
          <el-tab-pane label="设备状态" name="status">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>机场状态</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="工作模式">
                      <el-tag :type="getModeCodeType()">{{ getModeCodeText() }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="舱盖状态">
                      <el-tag :type="dockInfo.cover_state === 0 ? 'success' : 'warning'">
                        {{ dockInfo.cover_state === 0 ? '关闭' : '打开' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="推杆状态">
                      <el-tag :type="dockInfo.putter_state === 0 ? 'success' : 'warning'">
                        {{ getPutterStateText() }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="补光灯">
                      <el-tag :type="dockInfo.supplement_light_state ? 'primary' : 'info'">
                        {{ dockInfo.supplement_light_state ? '开启' : '关闭' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="急停状态">
                      <el-tag :type="dockInfo.emergency_stop_state ? 'danger' : 'success'">
                        {{ dockInfo.emergency_stop_state ? '急停' : '正常' }}
                      </el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="报警状态">
                      <el-tag :type="dockInfo.alarm_state ? 'danger' : 'success'">
                        {{ dockInfo.alarm_state ? '报警' : '正常' }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>电源信息</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="供电电压">{{ dockInfo.electric_supply_voltage }}V</el-descriptions-item>
                    <el-descriptions-item label="工作电压">{{ (dockInfo.working_voltage / 1000).toFixed(1) }}V</el-descriptions-item>
                    <el-descriptions-item label="工作电流">{{ dockInfo.working_current }}mA</el-descriptions-item>
                    <el-descriptions-item label="备用电池电压">{{ dockInfo.backup_battery && dockInfo.backup_battery.voltage ? (dockInfo.backup_battery.voltage / 1000).toFixed(1) : '-' }}V</el-descriptions-item>
                    <el-descriptions-item label="备用电池温度">{{ dockInfo.backup_battery && dockInfo.backup_battery.temperature || '-' }}°C</el-descriptions-item>
                    <el-descriptions-item label="备用电池开关">
                      <el-tag :type="dockInfo.backup_battery && dockInfo.backup_battery.switch ? 'success' : 'danger'">
                        {{ dockInfo.backup_battery && dockInfo.backup_battery.switch ? '开启' : '关闭' }}
                      </el-tag>
                    </el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>

          <!-- 任务信息 -->
          <el-tab-pane label="任务信息" name="task">
            <el-row :gutter="20">
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>当前任务</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="任务名称">{{ dockInfo.task_name || '暂无任务' }}</el-descriptions-item>
                    <el-descriptions-item label="任务步骤">
                      <el-tag :type="getFlightTaskStepType()">{{ getFlightTaskStepText() }}</el-tag>
                    </el-descriptions-item>
                    <el-descriptions-item label="准备进度">{{ dockInfo.flighttask_prepare_capacity }}%</el-descriptions-item>
                    <el-descriptions-item label="待上传文件">{{ dockInfo.media_file_detail && dockInfo.media_file_detail.remain_upload || 0 }}个</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
              <el-col :span="12">
                <el-card shadow="hover">
                  <div slot="header">
                    <span>统计信息</span>
                  </div>
                  <el-descriptions :column="1" border>
                    <el-descriptions-item label="任务次数">{{ dockInfo.job_number }}次</el-descriptions-item>
                    <el-descriptions-item label="累计时间">{{ formatAccTime(dockInfo.acc_time) }}</el-descriptions-item>
                    <el-descriptions-item label="首次开机">{{ formatTimestamp(dockInfo.first_power_on) }}</el-descriptions-item>
                    <el-descriptions-item label="激活时间">{{ formatTimestamp(dockInfo.activation_time) }}</el-descriptions-item>
                  </el-descriptions>
                </el-card>
              </el-col>
            </el-row>
          </el-tab-pane>
        </el-tabs>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>

    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />
  </div>
</template>

<script>
import { listDevice, getDevice, delDevice, addDevice, updateDevice, changeDeviceBindStatus, getDockInfo } from "@/api/device";

export default {
  name: "Device",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备表格数据
      deviceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示详情对话框
      detailOpen: false,
      // 当前查看的设备
      currentDevice: {},
      // 详情页面活跃标签
      activeTab: 'basic',
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        device_sn: null,
        device_name: null,
        nickname: null,
        workspace_id: null,
        control_source: null,
        type: null,
        bound_status: null,
        deviceType:3
      },
      // 列信息
      columns: [
        { key: 0, label: `设备序列号`, visible: true },
        { key: 1, label: `设备名称`, visible: true },
        { key: 2, label: `设备昵称`, visible: true },
        { key: 3, label: `工作空间ID`, visible: true },
        { key: 4, label: `工作空间名称`, visible: true },
        { key: 5, label: `控制源`, visible: true },
        { key: 6, label: `设备类型`, visible: true },
        { key: 7, label: `子类型`, visible: true },
        { key: 8, label: `域`, visible: true },
        { key: 9, label: `子设备序列号`, visible: true },
        { key: 10, label: `固件版本`, visible: true },
        { key: 11, label: `固件状态`, visible: true },
        { key: 12, label: `协议版本`, visible: true },
        { key: 13, label: `绑定状态`, visible: true },
        { key: 14, label: `绑定时间`, visible: true },
        { key: 15, label: `最后登录`, visible: true },
        { key: 16, label: `在线状态`, visible: true }
      ],
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        device_sn: [
          { required: true, message: "设备序列号不能为空", trigger: "blur" }
        ],
        device_name: [
          { required: true, message: "设备名称不能为空", trigger: "blur" }
        ],
        nickname: [
          { required: true, message: "设备昵称不能为空", trigger: "blur" }
        ]
      },
      dockInfo:{
		"network_state": {
			"type": 1,
			"quality": 5,
			"rate": 433.565
		},
		"drone_in_dock": true,
		"drone_charge_state": {
			"state": false,
			"capacity_percent": 94
		},
		"rainfall": 0,
		"wind_speed": 0,
		"environment_temperature": 43.5,
		"temperature": 48.1,
		"humidity": 48,
		"latitude": 28.181013,
		"longitude": 113.07996,
		"height": 42.157402,
		"alternate_land_point": {
			"latitude": 28.180973,
			"longitude": 113.0799,
			"safe_land_height": 30,
			"is_configured": true
		},
		"first_power_on": 1631945855969,
		"position_state": {
			"gps_number": 4,
			"is_fixed": 2,
			"quality": 5,
			"rtk_number": 28,
			"is_calibration": true
		},
		"storage": {
			"total": 82045336,
			"used": 51652
		},
		"mode_code": 0,
		"cover_state": 0,
		"supplement_light_state": false,
		"emergency_stop_state": false,
		"air_conditioner": {
			"air_conditioner_state": 1,
			"switch_time": 0
		},
		"battery_store_mode": 2,
		"alarm_state": false,
		"putter_state": 0,
		"sub_device": {
			"device_sn": "1581F5BMD232T001EKX6",
			"device_online_status": false,
			"device_paired": true,
			"device_model_key": "0-67-1"
		},
		"job_number": 1109,
		"acc_time": 54039095,
		"activation_time": 1686167989,
		"maintain_status": {
			"maintain_status_array": [
				{
					"last_maintain_time": 1768580128,
					"last_maintain_type": 17,
					"state": false
				},
				{
					"last_maintain_time": 1936027231,
					"last_maintain_type": 18,
					"state": true
				}
			]
		},
		"electric_supply_voltage": 230,
		"working_voltage": 26190,
		"working_current": 920,
		"backup_battery": {
			"voltage": 24794,
			"temperature": 51.2,
			"switch": true
		},
		"drone_battery_maintenance_info": {
			"batteries": [
				{
					"index": 0,
					"capacity_percent": 94,
					"voltage": 24976,
					"temperature": 45.4
				},
				{
					"index": 1,
					"capacity_percent": 94,
					"voltage": 24924,
					"temperature": 45.3
				}
			],
			"maintenance_state": 0,
			"maintenance_time_left": 0,
			"heat_state": 0
		},
		"flighttask_step_code": 5,
		"task_name": "暂无任务",
		"flighttask_prepare_capacity": 1,
		"media_file_detail": {
			"remain_upload": 0
		},
		"wireless_link": {
			"dongle_number": 1,
			"link_workmode": 0,
			"sdr_freq_band": 2.4,
			"sdr_link_state": false,
			"sdr_quality": 0,
			"4g_freq_band": 2.4,
			"4g_gnd_quality": 0,
			"4g_link_state": false,
			"4g_quality": 0,
			"4g_uav_quality": 0
		},
		"drc_state": 0,
		"user_experience_improvement": 2
	}




    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询设备列表 */
    getList() {
      this.loading = true;
      listDevice(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        // 根据实际API返回结构处理数据
        if (response.code === 0) {
          this.deviceList = response.data || [];
          this.total = response.data ? response.data.length : 0;
        } else {
          this.deviceList = [];
          this.total = 0;
          this.$modal.msgError(response.message || "获取设备列表失败");
        }
        this.loading = false;
      }).catch(error => {
        this.deviceList = [];
        this.total = 0;
        this.loading = false;
        this.$modal.msgError("获取设备列表失败");
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        device_sn: null,
        device_name: null,
        nickname: null,
        workspace_id: null,
        control_source: null,
        device_desc: null,
        child_device_sn: null,
        domain: 3,
        type: null,
        sub_type: 0,
        bound_status: false,
        firmware_version: null,
        firmware_status: 1,
        thing_version: null,
        normal_icon_url: "",
        selected_icon_url: "",
        mode_code:""
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.device_sn);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加设备";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const device_sn = row.device_sn || this.ids[0];
      if (row.device_sn) {
        // 直接使用行数据，处理icon_url嵌套结构
        this.form = Object.assign({}, row);
        if (row.icon_url) {
          this.form.normal_icon_url = row.icon_url.normal_icon_url || "";
          this.form.selected_icon_url = row.icon_url.selected_icon_url || "";
        }
        this.open = true;
        this.title = "修改设备";
      } else {
        // 从API获取数据
        getDevice(device_sn).then(response => {
          if (response.code === 0) {
            this.form = Object.assign({}, response.data);
            if (response.data.icon_url) {
              this.form.normal_icon_url = response.data.icon_url.normal_icon_url || "";
              this.form.selected_icon_url = response.data.icon_url.selected_icon_url || "";
            }
          } else {
            this.form = Object.assign({}, row);
            if (row.icon_url) {
              this.form.normal_icon_url = row.icon_url.normal_icon_url || "";
              this.form.selected_icon_url = row.icon_url.selected_icon_url || "";
            }
          }
          this.open = true;
          this.title = "修改设备";
        });
      }
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          // 构建提交数据，重新组装icon_url结构
          const submitData = Object.assign({}, this.form);
          submitData.icon_url = {
            normal_icon_url: this.form.normal_icon_url || "",
            selected_icon_url: this.form.selected_icon_url || ""
          };
          // 删除临时字段
          delete submitData.normal_icon_url;
          delete submitData.selected_icon_url;

          if (this.title === "修改设备") {
            updateDevice(submitData).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addDevice(submitData).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const device_sns = row.device_sn ? [row.device_sn] : this.ids;
      const deviceNames = row.device_sn ? [row.nickname || row.device_name] :
        this.deviceList.filter(item => device_sns.includes(item.device_sn))
          .map(item => item.nickname || item.device_name);

      this.$modal.confirm('是否确认删除设备"' + deviceNames.join('、') + '"？').then(function() {
        return delDevice(device_sns.join(','));
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    // 设备绑定状态修改
    handleBoundStatusChange(row) {
      let text = row.bound_status === true ? "绑定" : "解绑";
      this.$modal.confirm('确认要"' + text + '""' + row.nickname + '"设备吗？').then(function() {
        return changeDeviceBindStatus(row.device_sn, row.bound_status);
      }).then(() => {
        this.$modal.msgSuccess(text + "成功");
      }).catch(function() {
        row.bound_status = !row.bound_status;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('device/export', {
        ...this.queryParams
      }, `device_${new Date().getTime()}.xlsx`)
    },
    /** 查看按钮操作 */
    handleView(row) {
      this.currentDevice = Object.assign({}, row);
      this.activeTab = 'basic';
      this.detailOpen = true;
      // 获取机场设备详细信息
      this.getDockDeviceInfo(row.device_sn);
    },
    /** 获取机场设备信息 */
    getDockDeviceInfo(dockSn) {
      if (!dockSn) return;
      getDockInfo(dockSn).then(response => {
        if (response.code === 0 && response.data) {
          this.dockInfo = response.data;
        } else {
          console.warn('获取机场设备信息失败:', response.message);
        }
      }).catch(error => {
        console.error('获取机场设备信息出错:', error);
      });
    },
    // 获取网络类型名称
    getNetworkTypeName(type) {
      const typeMap = {
        1: '以太网',
        2: '4G',
        3: 'WiFi'
      };
      return typeMap[type] || '未知';
    },
    // 格式化存储容量
    formatStorage(bytes) {
      if (!bytes) return '0 B';
      const k = 1024;
      const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    },
    // 获取存储使用率
    getStorageUsagePercent() {
      if (!this.dockInfo.storage || !this.dockInfo.storage.total || !this.dockInfo.storage.used) return 0;
      return ((this.dockInfo.storage.used / this.dockInfo.storage.total) * 100).toFixed(1);
    },
    // 获取无人机电池维护状态类型
    getDroneBatteryMaintenanceStatusType() {
      const state = this.dockInfo.drone_battery_maintenance_info && this.dockInfo.drone_battery_maintenance_info.maintenance_state;
      const typeMap = {
        0: 'success', // 正常
        1: 'warning', // 维护中
        2: 'danger'   // 异常
      };
      return typeMap[state] || 'info';
    },
    // 获取无人机电池维护状态文本
    getDroneBatteryMaintenanceStatusText() {
      const state = this.dockInfo.drone_battery_maintenance_info && this.dockInfo.drone_battery_maintenance_info.maintenance_state;
      const textMap = {
        0: '正常',
        1: '维护中',
        2: '异常'
      };
      return textMap[state] || '未知';
    },
    // 获取工作模式类型
    getModeCodeType() {
      const mode = this.dockInfo.mode_code;
      if (mode === 0) return 'success'; // 待机
      if (mode >= 1 && mode <= 10) return 'primary'; // 工作中
      return 'warning'; // 其他状态
    },
    // 获取工作模式文本
    getModeCodeText() {
      const modeMap = {
        0: '待机',
        1: '远程调试',
        2: '升级中',
        3: '离线',
        4: '未知',
        5: '任务准备',
        6: '任务执行',
        7: '任务暂停',
        8: '任务完成',
        9: '任务取消',
        10: '任务异常'
      };
      return modeMap[this.dockInfo.mode_code] || '未知状态';
    },
    // 获取推杆状态文本
    getPutterStateText() {
      const stateMap = {
        0: '收起',
        1: '展开',
        2: '半展开'
      };
      return stateMap[this.dockInfo.putter_state] || '未知';
    },
    // 获取飞行任务步骤类型
    getFlightTaskStepType() {
      const step = this.dockInfo.flighttask_step_code;
      if (step === 0) return 'info'; // 无任务
      if (step >= 1 && step <= 3) return 'warning'; // 准备中
      if (step >= 4 && step <= 6) return 'primary'; // 执行中
      if (step === 7) return 'success'; // 完成
      return 'danger'; // 异常
    },
    // 获取飞行任务步骤文本
    getFlightTaskStepText() {
      const stepMap = {
        0: '无任务',
        1: '任务准备',
        2: '设备检查',
        3: '起飞准备',
        4: '起飞中',
        5: '任务执行',
        6: '返航中',
        7: '任务完成',
        8: '任务异常',
        9: '任务取消'
      };
      return stepMap[this.dockInfo.flighttask_step_code] || '未知状态';
    },
    // 格式化累计时间
    formatAccTime(seconds) {
      if (!seconds) return '0秒';
      const hours = Math.floor(seconds / 3600);
      const minutes = Math.floor((seconds % 3600) / 60);
      const secs = seconds % 60;

      let result = '';
      if (hours > 0) result += `${hours}小时`;
      if (minutes > 0) result += `${minutes}分钟`;
      if (secs > 0 || result === '') result += `${secs}秒`;

      return result;
    },
    // 格式化时间戳
    formatTimestamp(timestamp) {
      if (!timestamp) return '-';
      const date = new Date(timestamp * 1000);
      return date.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
      });
    }
  }
};
</script>

<style scoped>
.dock-detail-container {
  max-height: 70vh;
  overflow-y: auto;
}

.dock-detail-container .el-card {
  margin-bottom: 10px;
}

.dock-detail-container .el-descriptions {
  margin-bottom: 10px;
}

.dock-detail-container .el-table {
  margin-top: 10px;
}

.dock-detail-container .el-tabs__content {
  padding: 20px;
}

.dock-detail-container .el-row {
  margin-bottom: 20px;
}

.dock-detail-container .el-col {
  margin-bottom: 10px;
}
</style>
